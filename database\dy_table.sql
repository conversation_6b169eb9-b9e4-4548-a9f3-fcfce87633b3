-- 创建抖音微信管理表
USE sports_tracker;

-- 创建dy表，用于存储抖音微信记录
CREATE TABLE IF NOT EXISTS dy (
  id INT PRIMARY KEY AUTO_INCREMENT,
  phone VARCHAR(20) NOT NULL UNIQUE COMMENT '手机号',
  img LONGTEXT NOT NULL COMMENT '图片base64数据',
  date BIGINT NOT NULL COMMENT '到期时间戳',
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  INDEX idx_phone (phone),
  INDEX idx_date (date)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='抖音微信管理表';
