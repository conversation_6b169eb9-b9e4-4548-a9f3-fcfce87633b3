import * as qiniuService from '../services/qiniuService.js';
import path from 'path';
import crypto from 'crypto';

/**
 * 获取上传凭证
 * @param {Request} req 请求对象
 * @param {Response} res 响应对象
 */
export const getUploadToken = async (req, res) => {
  try {
    const { filename } = req.query;
    let key = null;
    
    if (filename) {
      // 生成唯一文件名
      const timestamp = new Date().getTime();
      const randomStr = crypto.randomBytes(8).toString('hex');
      const ext = path.extname(filename);
      key = `${timestamp}-${randomStr}${ext}`;
    }
    
    const token = qiniuService.getUploadToken(key);
    
    res.json({
      success: true,
      data: {
        uploadToken: token,
        key
      }
    });
  } catch (error) {
    console.error('获取上传凭证失败:', error);
    res.status(500).json({
      success: false,
      message: '获取上传凭证失败'
    });
  }
};

/**
 * 上传文件到七牛云
 * @param {Request} req 请求对象
 * @param {Response} res 响应对象
 */
export const uploadFile = async (req, res) => {
  try {
    if (!req.file) {
      return res.status(400).json({
        success: false,
        message: '请选择要上传的文件'
      });
    }
    
    const { originalname, path: tempFilePath } = req.file;
    
    // 生成唯一文件名
    const timestamp = new Date().getTime();
    const randomStr = crypto.randomBytes(8).toString('hex');
    const ext = path.extname(originalname);
    const key = `${timestamp}-${randomStr}${ext}`;
    
    const result = await qiniuService.uploadFile(tempFilePath, key);
    
    res.json({
      success: true,
      data: result
    });
  } catch (error) {
    console.error('上传文件失败:', error);
    res.status(500).json({
      success: false,
      message: '上传文件失败'
    });
  }
};

/**
 * 上传Base64图片
 * @param {Request} req 请求对象
 * @param {Response} res 响应对象
 */
export const uploadBase64 = async (req, res) => {
  try {
    const { base64Data, filename } = req.body;
    
    if (!base64Data) {
      return res.status(400).json({
        success: false,
        message: '请提供Base64图片数据'
      });
    }
    
    if (!filename) {
      return res.status(400).json({
        success: false,
        message: '请提供文件名'
      });
    }
    
    // 生成唯一文件名
    const timestamp = new Date().getTime();
    const randomStr = crypto.randomBytes(8).toString('hex');
    const ext = path.extname(filename);
    const key = `${timestamp}-${randomStr}${ext}`;
    
    const result = await qiniuService.uploadBase64(base64Data, key);
    
    res.json({
      success: true,
      data: result
    });
  } catch (error) {
    console.error('上传Base64图片失败:', error);
    res.status(500).json({
      success: false,
      message: '上传Base64图片失败'
    });
  }
};

/**
 * 批量上传Base64图片
 * @param {Request} req 请求对象
 * @param {Response} res 响应对象
 */
export const batchUploadBase64 = async (req, res) => {
  try {
    const { images } = req.body;
    
    if (!Array.isArray(images) || images.length === 0) {
      return res.status(400).json({
        success: false,
        message: '请提供有效的图片数据数组'
      });
    }
    
    const results = await Promise.all(
      images.map(async (item) => {
        try {
          const { base64Data, filename } = item;
          
          if (!base64Data || !filename) {
            return {
              original: filename || '未知文件',
              success: false,
              error: '缺少必要的图片数据或文件名'
            };
          }
          
          // 生成唯一文件名
          const timestamp = new Date().getTime();
          const randomStr = crypto.randomBytes(8).toString('hex');
          const ext = path.extname(filename);
          const key = `${timestamp}-${randomStr}${ext}`;
          
          const result = await qiniuService.uploadBase64(base64Data, key);
          
          return {
            original: filename,
            key: result.key,
            url: result.url,
            success: true
          };
        } catch (error) {
          return {
            original: item.filename || '未知文件',
            success: false,
            error: error.message
          };
        }
      })
    );
    
    res.json({
      success: true,
      data: results
    });
  } catch (error) {
    console.error('批量上传Base64图片失败:', error);
    res.status(500).json({
      success: false,
      message: '批量上传失败'
    });
  }
};

/**
 * 删除文件
 * @param {Request} req 请求对象
 * @param {Response} res 响应对象
 */
export const deleteFile = async (req, res) => {
  try {
    const { key } = req.params;
    
    if (!key) {
      return res.status(400).json({
        success: false,
        message: '请提供要删除的文件名'
      });
    }
    
    await qiniuService.deleteFile(key);
    
    res.json({
      success: true,
      message: '删除文件成功'
    });
  } catch (error) {
    console.error('删除文件失败:', error);
    res.status(500).json({
      success: false,
      message: '删除文件失败'
    });
  }
};

/**
 * 获取文件列表
 * @param {Request} req 请求对象
 * @param {Response} res 响应对象
 */
export const listFiles = async (req, res) => {
  try {
    const { prefix = '', marker = '', limit = 1000 } = req.query;
    
    const result = await qiniuService.listFiles(prefix, marker, parseInt(limit));
    
    res.json({
      success: true,
      data: result
    });
  } catch (error) {
    console.error('获取文件列表失败:', error);
    res.status(500).json({
      success: false,
      message: '获取文件列表失败'
    });
  }
};

/**
 * 刷新CDN缓存
 * @param {Request} req 请求对象
 * @param {Response} res 响应对象
 */
export const refreshCdn = async (req, res) => {
  try {
    const { urls } = req.body;
    
    if (!urls || !Array.isArray(urls) || urls.length === 0) {
      return res.status(400).json({
        success: false,
        message: '请提供要刷新的URL列表'
      });
    }
    
    const result = await qiniuService.refreshCdn(urls);
    
    res.json({
      success: true,
      data: result
    });
  } catch (error) {
    console.error('刷新CDN缓存失败:', error);
    res.status(500).json({
      success: false,
      message: '刷新CDN缓存失败'
    });
  }
}; 