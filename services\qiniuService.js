import qiniu from 'qiniu';
import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';
import qiniuConfig from '../config/qiniu.js';

// 初始化七牛配置
const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

const mac = new qiniu.auth.digest.Mac(qiniuConfig.accessKey, qiniuConfig.secretKey);

// 获取存储区域
const getZone = () => {
  switch (qiniuConfig.zone) {
    case 'Zone_z0': return qiniu.zone.Zone_z0;
    case 'Zone_z1': return qiniu.zone.Zone_z1;
    case 'Zone_z2': return qiniu.zone.Zone_z2;
    case 'Zone_na0': return qiniu.zone.Zone_na0;
    case 'Zone_as0': return qiniu.zone.Zone_as0;
    default: return qiniu.zone.Zone_z0;
  }
};

/**
 * 获取上传凭证
 * @param {string} key 文件名
 * @param {number} expires 过期时间，单位：秒，默认3600秒
 * @returns {string} 上传凭证
 */
export const getUploadToken = (key = null, expires = 3600) => {
  const options = {
    scope: key ? `${qiniuConfig.bucket}:${key}` : qiniuConfig.bucket,
    expires,
    returnBody: '{"key":"$(key)","hash":"$(etag)","fsize":$(fsize),"bucket":"$(bucket)","name":"$(x:name)"}'
  };
  const putPolicy = new qiniu.rs.PutPolicy(options);
  return putPolicy.uploadToken(mac);
};

/**
 * 获取文件下载链接
 * @param {string} key 文件名
 * @param {number} expires 过期时间，单位：秒，默认3600秒
 * @returns {string} 下载链接
 */
export const getDownloadUrl = (key, expires = 3600) => {
  const config = new qiniu.conf.Config();
  const bucketManager = new qiniu.rs.BucketManager(mac, config);
  return bucketManager.privateDownloadUrl(qiniuConfig.domain, key, expires);
};

/**
 * 上传本地文件到七牛云
 * @param {string} localFilePath 本地文件路径
 * @param {string} key 存储的文件名，如不指定则使用本地文件名
 * @returns {Promise} 上传结果
 */
export const uploadFile = (localFilePath, key = null) => {
  const config = new qiniu.conf.Config({
    zone: getZone()
  });
  
  const fileName = key || path.basename(localFilePath);
  const finalKey = path.join(qiniuConfig.uploadDir, fileName).replace(/\\/g, '/');
  const uploadToken = getUploadToken(finalKey);
  
  const formUploader = new qiniu.form_up.FormUploader(config);
  const putExtra = new qiniu.form_up.PutExtra();
  
  return new Promise((resolve, reject) => {
    formUploader.putFile(uploadToken, finalKey, localFilePath, putExtra, (err, respBody, respInfo) => {
      if (err) {
        reject(err);
        return;
      }
      
      if (respInfo.statusCode === 200) {
        resolve({
          ...respBody,
          url: `${qiniuConfig.domain}/${finalKey}`
        });
      } else {
        reject(new Error(`上传失败: ${respInfo.statusCode} ${JSON.stringify(respBody)}`));
      }
    });
  });
};

/**
 * 上传Buffer到七牛云
 * @param {Buffer} fileBuffer 文件Buffer
 * @param {string} key 存储的文件名
 * @returns {Promise} 上传结果
 */
export const uploadBuffer = (fileBuffer, key) => {
  const config = new qiniu.conf.Config({
    zone: getZone()
  });
  
  const finalKey = path.join(qiniuConfig.uploadDir, key).replace(/\\/g, '/');
  const uploadToken = getUploadToken(finalKey);
  
  const formUploader = new qiniu.form_up.FormUploader(config);
  const putExtra = new qiniu.form_up.PutExtra();
  
  return new Promise((resolve, reject) => {
    formUploader.put(uploadToken, finalKey, fileBuffer, putExtra, (err, respBody, respInfo) => {
      if (err) {
        reject(err);
        return;
      }
      
      if (respInfo.statusCode === 200) {
        resolve({
          ...respBody,
          url: `${qiniuConfig.domain}/${finalKey}`
        });
      } else {
        reject(new Error(`上传失败: ${respInfo.statusCode} ${JSON.stringify(respBody)}`));
      }
    });
  });
};

/**
 * 上传Base64图片到七牛云
 * @param {string} base64Data Base64图片数据
 * @param {string} key 存储的文件名
 * @returns {Promise} 上传结果
 */
export const uploadBase64 = (base64Data, key) => {
  // 去除Base64前缀数据
  const base64Content = base64Data.replace(/^data:[^;]+;base64,/, '');
  const fileBuffer = Buffer.from(base64Content, 'base64');
  return uploadBuffer(fileBuffer, key);
};

/**
 * 删除七牛云文件
 * @param {string} key 文件名
 * @returns {Promise} 删除结果
 */
export const deleteFile = (key) => {
  const config = new qiniu.conf.Config({
    zone: getZone()
  });
  const bucketManager = new qiniu.rs.BucketManager(mac, config);
  
  return new Promise((resolve, reject) => {
    bucketManager.delete(qiniuConfig.bucket, key, (err, respBody, respInfo) => {
      if (err) {
        reject(err);
        return;
      }
      
      if (respInfo.statusCode === 200) {
        resolve({ success: true });
      } else {
        reject(new Error(`删除失败: ${respInfo.statusCode} ${JSON.stringify(respBody)}`));
      }
    });
  });
};

/**
 * 批量删除七牛云文件
 * @param {Array<string>} keys 文件名数组
 * @returns {Promise} 删除结果
 */
export const batchDeleteFiles = (keys) => {
  const config = new qiniu.conf.Config({
    zone: getZone()
  });
  const bucketManager = new qiniu.rs.BucketManager(mac, config);
  
  const deleteOperations = keys.map(key => qiniu.rs.deleteOp(qiniuConfig.bucket, key));
  
  return new Promise((resolve, reject) => {
    bucketManager.batch(deleteOperations, (err, respBody, respInfo) => {
      if (err) {
        reject(err);
        return;
      }
      
      if (parseInt(respInfo.statusCode / 100) === 2) {
        resolve({ results: respBody });
      } else {
        reject(new Error(`批量删除失败: ${respInfo.statusCode} ${JSON.stringify(respBody)}`));
      }
    });
  });
};

/**
 * 获取文件信息
 * @param {string} key 文件名
 * @returns {Promise} 文件信息
 */
export const getFileInfo = (key) => {
  const config = new qiniu.conf.Config({
    zone: getZone()
  });
  const bucketManager = new qiniu.rs.BucketManager(mac, config);
  
  return new Promise((resolve, reject) => {
    bucketManager.stat(qiniuConfig.bucket, key, (err, respBody, respInfo) => {
      if (err) {
        reject(err);
        return;
      }
      
      if (respInfo.statusCode === 200) {
        resolve(respBody);
      } else {
        reject(new Error(`获取文件信息失败: ${respInfo.statusCode} ${JSON.stringify(respBody)}`));
      }
    });
  });
};

/**
 * 获取指定前缀的文件列表
 * @param {string} prefix 前缀
 * @param {string} marker 上一次列举返回的位置标记，作为本次列举的起点信息
 * @param {number} limit 每次返回的最大条目数
 * @returns {Promise} 文件列表
 */
export const listFiles = (prefix = '', marker = '', limit = 1000) => {
  const config = new qiniu.conf.Config({
    zone: getZone()
  });
  const bucketManager = new qiniu.rs.BucketManager(mac, config);
  
  return new Promise((resolve, reject) => {
    bucketManager.listPrefix(qiniuConfig.bucket, {
      prefix,
      marker,
      limit
    }, (err, respBody, respInfo) => {
      if (err) {
        reject(err);
        return;
      }
      
      if (respInfo.statusCode === 200) {
        resolve(respBody);
      } else {
        reject(new Error(`获取文件列表失败: ${respInfo.statusCode} ${JSON.stringify(respBody)}`));
      }
    });
  });
};

/**
 * 刷新CDN缓存
 * @param {Array<string>} urls 需要刷新的URL数组
 * @returns {Promise} 刷新结果
 */
export const refreshCdn = (urls) => {
  const cdnManager = new qiniu.cdn.CdnManager(mac);
  
  return new Promise((resolve, reject) => {
    cdnManager.refreshUrls(urls, (err, respBody, respInfo) => {
      if (err) {
        reject(err);
        return;
      }
      
      if (respInfo.statusCode === 200) {
        resolve(respBody);
      } else {
        reject(new Error(`刷新CDN失败: ${respInfo.statusCode} ${JSON.stringify(respBody)}`));
      }
    });
  });
};

/**
 * 设置文件生存时间
 * @param {string} key 文件名
 * @param {number} days 生存时间（天）
 * @returns {Promise} 设置结果
 */
export const setFileLifetime = (key, days) => {
  const config = new qiniu.conf.Config({
    zone: getZone()
  });
  const bucketManager = new qiniu.rs.BucketManager(mac, config);
  
  return new Promise((resolve, reject) => {
    bucketManager.deleteAfterDays(qiniuConfig.bucket, key, days, (err, respBody, respInfo) => {
      if (err) {
        reject(err);
        return;
      }
      
      if (respInfo.statusCode === 200) {
        resolve({ success: true });
      } else {
        reject(new Error(`设置文件生存时间失败: ${respInfo.statusCode} ${JSON.stringify(respBody)}`));
      }
    });
  });
}; 