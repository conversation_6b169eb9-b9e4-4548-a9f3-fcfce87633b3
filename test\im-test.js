import imService from '../services/imService.js'

/**
 * 测试IM服务配置
 */
async function testIMService() {
  console.log('开始测试IM服务...')
  
  try {
    // 1. 测试UserSig生成
    console.log('\n1. 测试UserSig生成...')
    const userSig = imService.generateUserSig('test_user')
    console.log('UserSig生成成功:', userSig ? '✓' : '✗')
    
    // 2. 测试管理员UserSig生成
    console.log('\n2. 测试管理员UserSig生成...')
    const adminUserSig = imService.generateAdminUserSig()
    console.log('管理员UserSig生成成功:', adminUserSig ? '✓' : '✗')
    
    // 3. 测试管理员账号导入
    console.log('\n3. 测试管理员账号导入...')
    const importResult = await imService.importAdminAccount()
    console.log('管理员账号导入结果:', importResult)
    
    if (importResult.ErrorCode === 0) {
      console.log('管理员账号导入成功 ✓')
    } else if (importResult.ErrorCode === 70402) {
      console.log('管理员账号已存在 ✓')
    } else {
      console.log('管理员账号导入失败 ✗')
      console.log('错误信息:', importResult.ErrorInfo)
    }
    
    // 4. 测试创建群组
    console.log('\n4. 测试创建群组...')
    const testGroupId = `test_group_${Date.now()}`
    const createResult = await imService.createGroup({
      groupId: testGroupId,
      groupName: '测试群组',
      groupType: 'Public',
      maxMemberCount: 10,
      ownerAccount: '1',
      introduction: '这是一个测试群组',
      notification: '欢迎加入测试群组'
    })
    
    console.log('创建群组结果:', createResult)
    
    if (createResult.ErrorCode === 0) {
      console.log('群组创建成功 ✓')
      
      // 5. 测试解散群组
      console.log('\n5. 测试解散群组...')
      const destroyResult = await imService.destroyGroup(testGroupId)
      console.log('解散群组结果:', destroyResult)
      
      if (destroyResult.ErrorCode === 0) {
        console.log('群组解散成功 ✓')
      } else {
        console.log('群组解散失败 ✗')
        console.log('错误信息:', destroyResult.ErrorInfo)
      }
    } else {
      console.log('群组创建失败 ✗')
      console.log('错误信息:', createResult.ErrorInfo)
    }
    
    console.log('\n✅ IM服务测试完成')
    
  } catch (error) {
    console.error('\n❌ IM服务测试失败:', error)
  }
}

// 运行测试
testIMService()
