import express from 'express';
import cors from 'cors';
import authRoutes from './routes/auth.js';
import sportsRoutes from './routes/sports.js';
import qiniuRoutes from './routes/qiniu.js';
import imRoutes from './routes/im.js';
import { updateExpiredEvents } from './routes/sports.js';
import { checkAndSendReminders } from './routes/sports.js';
import imService from './services/imService.js';
// import dotenv from 'dotenv';

// 加载环境变量
// dotenv.config();

const app = express();
const PORT = process.env.PORT || 3000;

// 中间件
app.use(cors());
app.use(express.json({limit: '50mb'})) // <-- 这一行
app.use(express.urlencoded({ extended: true, limit: '50mb' }));

// 路由
app.use('/api/auth', authRoutes);
app.use('/api/sports', sportsRoutes);
app.use('/api/qiniu', qiniuRoutes);
app.use('/api/im', imRoutes);

// 定期检查过期的活动（每小时检查一次）
setInterval(async () => {
  try {
    console.log('开始检查过期活动...');
    const updatedCount = await updateExpiredEvents();
    console.log(`已将 ${updatedCount} 个过期活动状态更新为 'ended'`);
  } catch (error) {
    console.error('检查过期活动时出错:', error);
  }
}, 30 * 60 * 1000); // 60分钟 * 60秒 * 1000毫秒

// 定期检查即将开始的活动并发送提醒（每5分钟检查一次）
setInterval(async () => {
  try {
    console.log('开始检查即将开始的活动...');
    const sentCount = await checkAndSendReminders();
    console.log(`已为 ${sentCount} 个即将开始的活动发送提醒`);
  } catch (error) {
    console.error('发送活动提醒时出错:', error);
  }
}, 5 * 60 * 1000); // 5分钟 * 60秒 * 1000毫秒
// 5 * 60 * 1000
// 错误处理中间件
app.use((err, req, res, next) => {
  console.log(err);
  
  res.status(500).json({
    success: false,
    message: '服务器内部错误',
  });
});

app.listen(PORT, async () => {
  console.log(`服务器运行在端口 ${PORT}`);

  // 初始化IM服务
  await imService.initializeService();
});