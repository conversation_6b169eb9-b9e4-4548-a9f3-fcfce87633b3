# 七牛云CDN模块使用文档

本模块提供了完整的七牛云CDN文件管理功能，包括上传、删除、获取文件列表等操作。

## 配置

配置文件位于 `config/qiniu.js`，需要设置以下参数：

```js
export default {
  accessKey: 'your_access_key',
  secretKey: 'your_secret_key',
  bucket: 'your_bucket_name',
  domain: 'your_cdn_domain',  // 如：http://cdn.example.com
  zone: 'Zone_z0',  // 存储区域，如：z0(华东)、z1(华北)、z2(华南)、na0(北美)、as0(东南亚)
  uploadDir: 'uploads/'  // 存储路径前缀
}
```

请在使用前完成配置。

## API接口

### 1. 获取上传凭证

用于前端直传七牛云

- 请求方式：GET
- 路径：`/api/qiniu/upload-token`
- 参数：
  - filename: 文件名（可选）
- 返回示例：
```json
{
  "success": true,
  "data": {
    "uploadToken": "上传凭证",
    "key": "生成的唯一文件名(如提供filename参数则存在)"
  }
}
```

### 2. 上传文件

通过服务器中转上传文件到七牛云

- 请求方式：POST
- 路径：`/api/qiniu/upload`
- 参数：
  - file: 文件对象（form-data表单方式）
- 返回示例：
```json
{
  "success": true,
  "data": {
    "key": "文件名",
    "hash": "文件hash",
    "fsize": 文件大小,
    "bucket": "存储空间",
    "name": "文件名",
    "url": "文件URL"
  }
}
```

### 3. 上传Base64图片

上传Base64编码的图片到七牛云

- 请求方式：POST
- 路径：`/api/qiniu/upload-base64`
- 参数：
  - base64Data: Base64编码的图片数据
  - filename: 文件名
- 返回示例：
```json
{
  "success": true,
  "data": {
    "key": "文件名",
    "hash": "文件hash",
    "fsize": 文件大小,
    "bucket": "存储空间",
    "name": "文件名",
    "url": "文件URL"
  }
}
```

### 4. 批量上传Base64图片

批量上传多个Base64编码的图片到七牛云

- 请求方式：POST
- 路径：`/api/qiniu/batch-upload-base64`
- 参数：
  - images: 图片数组，每个元素包含：
    - base64Data: Base64编码的图片数据
    - filename: 文件名
- 返回示例：
```json
{
  "success": true,
  "data": [
    {
      "original": "原始文件名1",
      "key": "文件名1",
      "url": "文件URL1",
      "success": true
    },
    {
      "original": "原始文件名2",
      "key": "文件名2",
      "url": "文件URL2",
      "success": true
    },
    {
      "original": "原始文件名3",
      "success": false,
      "error": "错误信息"
    }
  ]
}
```

### 5. 删除文件

删除七牛云上的文件

- 请求方式：DELETE
- 路径：`/api/qiniu/file/:key`
- 参数：
  - key: 文件名（路径参数）
- 返回示例：
```json
{
  "success": true,
  "message": "删除文件成功"
}
```

### 6. 获取文件列表

获取七牛云上的文件列表

- 请求方式：GET
- 路径：`/api/qiniu/files`
- 参数：
  - prefix: 文件名前缀（可选）
  - marker: 上一次列举返回的位置标记（可选）
  - limit: 每次返回的最大条目数（可选，默认1000）
- 返回示例：
```json
{
  "success": true,
  "data": {
    "items": [
      {
        "key": "文件名",
        "hash": "文件hash",
        "fsize": 文件大小,
        "mimeType": "文件类型",
        "putTime": 上传时间,
        "type": 存储类型,
        "status": 状态
      }
    ],
    "marker": "下一次列举的位置标记",
    "commonPrefixes": [
      "公共前缀列表"
    ]
  }
}
```

### 7. 刷新CDN缓存

刷新七牛云CDN缓存

- 请求方式：POST
- 路径：`/api/qiniu/refresh-cdn`
- 参数：
  - urls: 需要刷新的URL数组
- 返回示例：
```json
{
  "success": true,
  "data": {
    "code": 200,
    "error": "错误信息",
    "requestId": "请求ID",
    "urlQuotaDay": 每日限额,
    "urlSurplusDay": 每日剩余额度,
    "dirQuotaDay": 每日目录限额,
    "dirSurplusDay": 每日目录剩余限额
  }
}
```

## 服务端API

服务端可以导入 `services/qiniuService.js` 获取更多七牛云操作功能：

```js
import * as qiniuService from '../services/qiniuService.js';

// 获取上传凭证
const token = qiniuService.getUploadToken();

// 上传本地文件
const result = await qiniuService.uploadFile('/path/to/file.jpg', 'custom-filename.jpg');

// 上传Buffer
const buffer = Buffer.from('file content');
const bufferResult = await qiniuService.uploadBuffer(buffer, 'filename.txt');

// 上传Base64图片
const base64Result = await qiniuService.uploadBase64('data:image/jpeg;base64,...', 'image.jpg');

// 删除文件
await qiniuService.deleteFile('filename.jpg');

// 批量删除文件
await qiniuService.batchDeleteFiles(['file1.jpg', 'file2.jpg']);

// 获取文件信息
const fileInfo = await qiniuService.getFileInfo('filename.jpg');

// 获取文件列表
const fileList = await qiniuService.listFiles('prefix', '', 100);

// 刷新CDN缓存
await qiniuService.refreshCdn(['http://example.com/file1.jpg', 'http://example.com/file2.jpg']);

// 设置文件生存时间
await qiniuService.setFileLifetime('filename.jpg', 7); // 7天后自动删除
```

## 批量上传Base64图片示例

前端批量上传Base64图片示例：

```javascript
// 前端多图片上传
async function uploadMultipleImages(imageFiles) {
  const base64Array = [];
  
  // 转换所有图片为Base64
  for (const file of imageFiles) {
    const base64 = await fileToBase64(file);
    base64Array.push({
      filename: file.name,
      base64Data: base64
    });
  }
  
  // 发送到后端
  const response = await fetch('/api/qiniu/batch-upload-base64', {
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify({ images: base64Array })
  });
  
  return await response.json();
}

// 文件转Base64
function fileToBase64(file) {
  return new Promise((resolve, reject) => {
    const reader = new FileReader();
    reader.onload = () => resolve(reader.result);
    reader.onerror = reject;
    reader.readAsDataURL(file);
  });
}

// 使用示例
document.querySelector('#files-input').addEventListener('change', async (e) => {
  const files = e.target.files;
  if (files.length > 0) {
    try {
      const result = await uploadMultipleImages(Array.from(files));
      console.log('批量上传成功:', result.data);
      
      // 处理上传结果
      result.data.forEach(item => {
        if (item.success) {
          console.log(`文件 ${item.original} 上传成功，URL: ${item.url}`);
        } else {
          console.error(`文件 ${item.original} 上传失败: ${item.error}`);
        }
      });
    } catch (error) {
      console.error('批量上传失败:', error);
    }
  }
});
```

## 前端直传七牛云示例

```javascript
// 1. 从服务器获取上传凭证
async function getUploadToken(filename) {
  const response = await fetch(`/api/qiniu/upload-token?filename=${encodeURIComponent(filename)}`);
  const result = await response.json();
  return result.data;
}

// 2. 使用凭证上传文件
async function uploadToQiniu(file) {
  const { uploadToken, key } = await getUploadToken(file.name);
  
  const formData = new FormData();
  formData.append('token', uploadToken);
  formData.append('key', key);
  formData.append('file', file);
  formData.append('fname', file.name);
  
  const response = await fetch('https://up.qiniup.com', { // 根据存储区域选择不同上传域名
    method: 'POST',
    body: formData
  });
  
  const result = await response.json();
  // 返回的result中包含key等信息，完整URL需要自行拼接
  result.url = `您的CDN域名/${result.key}`;
  return result;
}

// 使用示例
document.querySelector('#file-input').addEventListener('change', async (e) => {
  const file = e.target.files[0];
  if (file) {
    try {
      const result = await uploadToQiniu(file);
      console.log('上传成功:', result.url);
    } catch (error) {
      console.error('上传失败:', error);
    }
  }
});
```

## 注意事项

1. 请妥善保管七牛云的 accessKey 和 secretKey，不要暴露在前端代码中
2. 上传大文件时，建议使用前端直传方式，减轻服务器负担
3. 刷新CDN缓存有每日次数限制，请合理使用
4. 生成上传凭证时可以通过配置选项设置上传策略，如文件大小限制、文件类型限制等
5. 批量上传Base64图片时，注意控制并发数量，避免服务器负载过高 