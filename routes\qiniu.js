import express from 'express';
import * as qiniuController from '../controllers/qiniuController.js';
import multer from 'multer';
import path from 'path';
import { fileURLToPath } from 'url';
import fs from 'fs';

const router = express.Router();
const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// 创建上传临时目录
const uploadDir = path.join(__dirname, '../uploads');
if (!fs.existsSync(uploadDir)) {
  fs.mkdirSync(uploadDir, { recursive: true });
}

// 配置multer存储
const storage = multer.diskStorage({
  destination: (req, file, cb) => {
    cb(null, uploadDir);
  },
  filename: (req, file, cb) => {
    const timestamp = new Date().getTime();
    const originalname = file.originalname;
    cb(null, `${timestamp}-${originalname}`);
  },
});

const upload = multer({ storage });

// 获取上传凭证
router.get('/upload-token', qiniuController.getUploadToken);

// 上传文件
router.post('/upload', upload.single('file'), qiniuController.uploadFile);

// 上传Base64图片
router.post('/upload-base64', qiniuController.uploadBase64);

// 批量上传Base64图片
router.post('/batch-upload-base64', qiniuController.batchUploadBase64);

// 删除文件
router.delete('/file/:key', qiniuController.deleteFile);

// 获取文件列表
router.get('/files', qiniuController.listFiles);

// 刷新CDN缓存
router.post('/refresh-cdn', qiniuController.refreshCdn);

export default router; 