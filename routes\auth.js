import express from 'express'
import bcrypt from 'bcryptjs'
import jwt from 'jsonwebtoken'
import db from '../config/db.js'
import axios from 'axios'
import auth from '../middleware/auth.js'
import imService from '../services/imService.js'
import imConfig from '../config/im.js'
const secretKey = 'liangxi^_^'
const router = express.Router()

router.get('/verify', auth, async (req, res) => {
  let [users] = await db.execute('SELECT * FROM user_with_published_count WHERE id = ?', [req.user.id])
  if (users.length === 0) {
    return res.send({ code: 500, msg: '用户不存在' })
  }
  const user = users[0]
  const token = jwt.sign(user, secretKey, {
    expiresIn: '7d'
  })

  // 生成IM配置
  const imConfigData = {
    SDKAppID: imConfig.SDKAppID,
    userID: user.id.toString(),
    userSig: imService.generateUserSig(user.id.toString())
  }

  res.send({
    code: 200,
    msg: '登录成功',
    data: {
      token: 'Bearer ' + token,
      user: user,
      imConfig: imConfigData
    }
  })
})

router.post('/wxLogin', async (req, res) => {
  const { code } = req.body
  if (!code) {
    return res.send({ code: 500, msg: '缺少code参数' })
  }
  const appId = 'wx824ca95bc88ecfee'
  const appSecret = '45726246f4ccd4b9bdfb07d25137567e'
  if (!appId || !appSecret) {
    return res.status(500).json({ success: false, message: '未配置微信AppID或Secret' })
  }
  try {
    const wxResponse = await axios.get(`https://api.weixin.qq.com/sns/jscode2session?appid=${appId}&secret=${appSecret}&js_code=${code}&grant_type=authorization_code`)
 
    const { openid, session_key } = wxResponse.data
    if (!openid || !session_key) {
      return res.send({ code: 500, msg: '获取openid失败' })
    }
    let [users] = await db.execute('SELECT * FROM user_with_published_count WHERE openid = ?', [openid])
 
    if (users.length === 0) {
      const result = await db.execute('INSERT INTO users(openid,nickname,avatar) VALUES (?,?,?)', [openid, '', ''])
    
      if (result[0].affectedRows > 0) {
        [users] = await db.execute('SELECT * FROM user_with_published_count WHERE openid = ?', [openid])
      } else {
        return res.send({ code: 500, msg: '用户注册失败' })
      }
    }
    const user = users[0]
    const token = jwt.sign(user, secretKey, {
      expiresIn: '7d'
    })

    // 生成IM配置
    const imConfigData = {
      SDKAppID: imConfig.SDKAppID,
      userID: user.id.toString(),
      userSig: imService.generateUserSig(user.id.toString())
    }

    res.send({
      code: 200,
      msg: '登录成功',
      data: {
        token: 'Bearer ' + token,
        user: user,
        imConfig: imConfigData
      }
    })
  } catch (error) {
    res.status(500).json({ success: false, message: '服务器内部错误' })
  }
})

// 获取IM配置
router.get('/imConfig', auth, async (req, res) => {
  try {
    const imConfigData = {
      SDKAppID: imConfig.SDKAppID,
      userID: req.user.id.toString(),
      userSig: imService.generateUserSig(req.user.id.toString())
    }

    res.send({
      code: 200,
      msg: '获取IM配置成功',
      data: imConfigData
    })
  } catch (error) {
    console.error('获取IM配置失败:', error)
    res.send({ code: 500, msg: '获取IM配置失败' })
  }
})

// 修改用户信息
router.post('/updateUserInfo', auth, async (req, res) => {
  const { nickname, avatar,wechat,phone } = req.body

  if (!nickname || !avatar) {
    return res.send({ code: 500, msg: '缺少参数' })
  }
  try {
    let result = await db.execute('UPDATE users SET nickname = ?, avatar = ?,phone=?,wechat=? WHERE id = ?', [nickname, avatar, phone,wechat, req.user.id])
    // 如果result添加成功则返回200，否则返回500
    if (result[0].affectedRows > 0) {
      res.send({ code: 200, msg: '修改成功' })
    }
  } catch (error) {
    res.status(500).json({ success: false, message: '服务器内部错误' })
  }
})
router.get("/getDyWx",(req,res)=>{
  let {phone} = req.query
  if(!phone){
    return res.send({code:500,msg:'缺少参数'})
  }
  db.execute('SELECT * FROM dy where phone = ?',[phone])
  .then((result)=>{
    res.send({code:200,msg:'获取成功',data:result[0]})
  })
  .catch((err)=>{
    res.send({code:500,msg:'获取失败'})
  })
})

// 添加抖音微信记录
router.post("/addDyWx", (req, res) => {
  let { phone, img, date } = req.body
  if (!phone || !img || !date) {
    return res.send({ code: 500, msg: '缺少参数' })
  }
  db.execute('INSERT INTO dy (phone, img, date) VALUES (?, ?, ?)', [phone, img, date])
    .then((result) => {
      if (result[0].affectedRows > 0) {
        res.send({ code: 200, msg: '添加成功' })
      } else {
        res.send({ code: 500, msg: '添加失败' })
      }
    })
    .catch((err) => {
      res.send({ code: 500, msg: '添加失败' })
    })
})

// 更新抖音微信记录
router.post("/updateDyWx", (req, res) => {
  let { phone, img, date } = req.body
  if (!phone) {
    return res.send({ code: 500, msg: '缺少phone参数' })
  }
  db.execute('UPDATE dy SET img = ?, date = ? WHERE phone = ?', [img, date, phone])
    .then((result) => {
      if (result[0].affectedRows > 0) {
        res.send({ code: 200, msg: '更新成功' })
      } else {
        res.send({ code: 500, msg: '记录不存在' })
      }
    })
    .catch((err) => {
      res.send({ code: 500, msg: '更新失败' })
    })
})

// 删除抖音微信记录
router.delete("/deleteDyWx", (req, res) => {
  let { phone } = req.query
  if (!phone) {
    return res.send({ code: 500, msg: '缺少phone参数' })
  }
  db.execute('DELETE FROM dy WHERE phone = ?', [phone])
    .then((result) => {
      if (result[0].affectedRows > 0) {
        res.send({ code: 200, msg: '删除成功' })
      } else {
        res.send({ code: 500, msg: '记录不存在' })
      }
    })
    .catch((err) => {
      res.send({ code: 500, msg: '删除失败' })
    })
})

// 获取所有抖音微信记录
router.get("/getAll", (req, res) => {
  db.execute('SELECT * FROM dy ORDER BY date DESC')
    .then((result) => {
      res.send({ code: 200, msg: '获取成功', data: result[0] })
    })
    .catch((err) => {
      console.error('获取所有记录失败:', err)
      res.send({ code: 500, msg: '获取失败' })
    })
})

export default router
