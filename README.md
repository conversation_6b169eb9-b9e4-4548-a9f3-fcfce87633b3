# 运动追踪系统后端服务

这是一个基于 Node.js + Express + MySQL + JWT 的运动追踪系统后端服务。

## 功能特点

- 用户认证（注册/登录）
- JWT token 认证
- 运动记录的 CRUD 操作
- 数据持久化存储
- RESTful API 设计

## 技术栈

- Node.js
- Express.js
- MySQL2
- JWT (JSON Web Token)
- bcryptjs (密码加密)

## 安装和运行

1. 安装依赖：
```bash
npm install
```

2. 配置环境变量：
创建 `.env` 文件，并填写相应的配置信息：
```
DB_HOST=localhost
DB_USER=your_username
DB_PASSWORD=your_password
DB_NAME=sports_tracker
JWT_SECRET=your_jwt_secret
PORT=3000
```

3. 初始化数据库：
使用 MySQL 客户端执行 `database/init.sql` 文件中的 SQL 语句。

4. 启动服务：
```bash
# 开发模式
npm run dev

# 生产模式
npm start
```

## API 接口

### 认证接口

- POST /api/auth/register - 用户注册
- POST /api/auth/login - 用户登录

### 运动记录接口

- GET /api/sports - 获取运动记录列表
- POST /api/sports - 创建新的运动记录
- PUT /api/sports/:id - 更新运动记录
- DELETE /api/sports/:id - 删除运动记录

## 微信订阅消息功能

系统支持在约球活动开始前30分钟通过微信小程序订阅消息提醒参与者。

### 配置步骤

1. 在微信公众平台开通订阅消息服务
2. 创建订阅消息模板，模板包含以下字段：
   - thing1: 活动名称
   - thing2: 活动地点
   - time3: 活动时间
   - thing4: 备注信息
3. 配置环境变量：
   ```
   WECHAT_APP_ID=小程序appId
   WECHAT_APP_SECRET=小程序appSecret
   WECHAT_TEMPLATE_ID=订阅消息模板ID
   ```

### API接口

- `POST /api/sports/setSubscription` - 设置用户是否接收订阅消息及更新openId
  ```json
  {
    "subscribeEvent": true,
    "openId": "wx_open_id"
  }
  ```

- `POST /api/sports/checkEventReminders` - 手动触发检查和发送提醒
  ```json
  {
    "apiKey": "your_secure_api_key"
  }
  ```

### 自动提醒

系统每5分钟自动检查一次即将开始的活动，并发送提醒消息给已订阅的用户。

## 数据库结构

### users 表
- id: 用户ID
- username: 用户名
- password: 密码（加密存储）
- email: 邮箱
- created_at: 创建时间
- updated_at: 更新时间

### sports_records 表
- id: 记录ID
- user_id: 用户ID（外键）
- sport_type: 运动类型
- duration: 运动时长（分钟）
- distance: 运动距离（公里）
- calories: 消耗卡路里
- location: 运动地点
- notes: 备注
- created_at: 创建时间
- updated_at: 更新时间 