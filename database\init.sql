-- 创建数据库
CREATE DATABASE IF NOT EXISTS sports_tracker;
USE sports_tracker;

-- 创建用户表
CREATE TABLE IF NOT EXISTS users (
  id INT PRIMARY KEY AUTO_INCREMENT,
  username VARCHAR(50) NOT NULL UNIQUE,
  password VARCHAR(255) NOT NULL,
  email VARCHAR(100) NOT NULL UNIQUE,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- 创建运动记录表
CREATE TABLE IF NOT EXISTS sports_records (
  id INT PRIMARY KEY AUTO_INCREMENT,
  user_id INT NOT NULL,
  sport_type VARCHAR(50) NOT NULL,
  duration INT NOT NULL COMMENT '运动时长（分钟）',
  distance DECIMAL(10,2) COMMENT '运动距离（公里）',
  calories INT COMMENT '消耗卡路里',
  location VARCHAR(255) COMMENT '运动地点',
  notes TEXT COMMENT '备注',
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
); 