import express from 'express';
import cors from 'cors';
import authRoutes from './routes/auth.js';
import sportsRoutes from './routes/sports.js';
import qiniuRoutes from './routes/qiniu.js';

import {createServer} from "https"
import fs from 'fs'

const app = express();

// 中间件
app.use(cors());
app.use(express.json({limit: '50mb'})) // <-- 这一行
app.use(express.urlencoded({ extended: true }));

// 路由
app.use('/api/auth', authRoutes);
app.use('/api/sports', sportsRoutes);
app.use('/api/qiniu', qiniuRoutes);

// 错误处理中间件
app.use((err, req, res, next) => {
  console.log(err);
  
  res.status(500).json({
    success: false,
    message: '服务器内部错误',
  });
});

const httpServer = createServer({
    key: fs.readFileSync("./SSL/api.xliangxi.vip.key"),
    cert: fs.readFileSync("./SSL/api.xliangxi.vip.pem"),
  },
  app
);

const PORT =3000;
httpServer.listen(PORT, () => {
  console.log(`服务器运行在端口 ${PORT}`);
}); 