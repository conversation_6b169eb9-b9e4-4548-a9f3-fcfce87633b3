//函数

CREATE DEFINER=`root`@`localhost` FUNCTION `fn_distance_sphere`(
    lat1 DOUBLE,  -- 第一个点的纬度
    lng1 DOUBLE,  -- 第一个点的经度
    lat2 DOUBLE,  -- 第二个点的纬度
    lng2 DOUBLE   -- 第二个点的经度
) RETURNS double
    DETERMINISTIC
BEGIN
    -- 使用ST_Distance_Sphere计算两点间距离(单位:米)
    RETURN ST_Distance_Sphere(POINT(lng1, lat1), POINT(lng2, lat2));
END



//查询

SELECT ST_Distance_Sphere(
    POINT(108.883888, 34.195509),  
    POINT(108.446064, 34.273938)   
) AS distance_in_meters;



SELECT 
    *,
    ST_Distance_Sphere(
        POINT(108.93984, 34.34127),  -- 参考点(北京天安门)
        POINT(longitude, latitude)          -- 表中的点
    ) AS distance
FROM 
    sports_records
WHERE 
    ST_Distance_Sphere(
        POINT(108.93984, 34.34127),
        POINT(longitude, latitude)
    ) <= 20000  -- 1000米范围内
ORDER BY 
    distance;
		


SELECT 
    *,
    ROUND(fn_distance_sphere(34.34127, 108.93984, latitude, longitude), 2) AS distance_meters
FROM 
    sports_records
WHERE 
    fn_distance_sphere(34.34127, 108.93984, latitude, longitude) <= 20000  -- 1000米=1公里
ORDER BY 
    distance_meters;
		
		
		
SELECT *,ROUND(fn_distance_sphere(34.34127, 108.93984, latitude, longitude), 2) AS distance_meters FROM sports_records WHERE type =1 AND fn_distance_sphere(34.34127, 108.93984, latitude, longitude) <= 200000  ORDER BY distance_meters LIMIT 0,10;

触发器

DELIMITER //
CREATE TRIGGER update_published_count 
AFTER INSERT ON 发布表
FOR EACH ROW 
BEGIN
    UPDATE user 
    SET 已发布 = (
        SELECT COUNT(*) 
        FROM 发布表 
        WHERE user_id = NEW.user_id
    )
    WHERE id = NEW.user_id;
END//
DELIMITER ;

-- 同样需要为DELETE操作创建触发器
DELIMITER //
CREATE TRIGGER update_published_count_delete 
AFTER DELETE ON 发布表
FOR EACH ROW 
BEGIN
    UPDATE user 
    SET 已发布 = (
        SELECT COUNT(*) 
        FROM 发布表 
        WHERE user_id = OLD.user_id
    )
    WHERE id = OLD.user_id;
END//
DELIMITER ;

视图
CREATE VIEW current_join_events_people AS
SELECT u.*, 
       (SELECT COUNT(*) FROM sports_records p WHERE p.user_id = u.id) AS total_posts
FROM users u;
