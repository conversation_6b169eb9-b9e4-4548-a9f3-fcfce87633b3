import jwt from 'jsonwebtoken';
const secretKey = 'liangxi^_^'
const auth = (req, res, next) => {
  try {
    const token = req.header('Authorization')?.replace('Bearer ', '');
    
    if (!token) {
      return res.send({
        status:400,
        message: '未提供认证令牌'
      })
     
    }

    const decoded = jwt.verify(token, secretKey);
    req.user = decoded;
    next();
  } catch (error) {
   
    res.send({
      status:400,
      message: '无效的认证令牌'
    })
  }
};

export default auth;