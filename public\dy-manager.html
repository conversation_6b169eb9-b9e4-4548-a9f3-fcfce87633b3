<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>抖音微信管理</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: #f5f5f5;
            color: #333;
            line-height: 1.6;
        }

        .container {
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }

        .header {
            text-align: center;
            margin-bottom: 30px;
        }

        .header h1 {
            color: #2c3e50;
            font-weight: 300;
            font-size: 2rem;
        }

        .card {
            background: white;
            border-radius: 12px;
            box-shadow: 0 2px 20px rgba(0,0,0,0.1);
            padding: 25px;
            margin-bottom: 20px;
        }

        .form-group {
            margin-bottom: 20px;
        }

        label {
            display: block;
            margin-bottom: 8px;
            font-weight: 500;
            color: #555;
        }

        input[type="text"], input[type="tel"], input[type="url"], input[type="datetime-local"] {
            width: 100%;
            padding: 12px 16px;
            border: 2px solid #e1e8ed;
            border-radius: 8px;
            font-size: 16px;
            transition: all 0.3s ease;
            background: #fff;
            color: #333;
        }

        input:focus {
            outline: none;
            border-color: #3498db;
            box-shadow: 0 0 0 3px rgba(52, 152, 219, 0.1);
            transform: translateY(-1px);
        }

        /* 美化时间选择器 */
        input[type="datetime-local"] {
            position: relative;
            background: linear-gradient(135deg, #fff 0%, #f8f9fa 100%);
            border: 2px solid #e1e8ed;
            cursor: pointer;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
        }

        input[type="datetime-local"]:hover {
            border-color: #3498db;
            background: linear-gradient(135deg, #fff 0%, #f0f8ff 100%);
        }

        input[type="datetime-local"]:focus {
            border-color: #3498db;
            box-shadow: 0 0 0 3px rgba(52, 152, 219, 0.1);
            background: #fff;
        }

        /* 时间选择器图标美化 */
        input[type="datetime-local"]::-webkit-calendar-picker-indicator {
            background: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="%233498db" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><rect x="3" y="4" width="18" height="18" rx="2" ry="2"></rect><line x1="16" y1="2" x2="16" y2="6"></line><line x1="8" y1="2" x2="8" y2="6"></line><line x1="3" y1="10" x2="21" y2="10"></line></svg>') no-repeat center;
            background-size: 16px 16px;
            cursor: pointer;
            opacity: 0.7;
            transition: opacity 0.3s ease;
            width: 20px;
            height: 20px;
        }

        input[type="datetime-local"]::-webkit-calendar-picker-indicator:hover {
            opacity: 1;
        }

        /* 时间输入框的内部样式 */
        input[type="datetime-local"]::-webkit-datetime-edit {
            padding: 0;
            color: #333;
        }

        input[type="datetime-local"]::-webkit-datetime-edit-fields-wrapper {
            padding: 0;
        }

        input[type="datetime-local"]::-webkit-datetime-edit-text {
            color: #666;
            padding: 0 2px;
        }

        input[type="datetime-local"]::-webkit-datetime-edit-month-field,
        input[type="datetime-local"]::-webkit-datetime-edit-day-field,
        input[type="datetime-local"]::-webkit-datetime-edit-year-field,
        input[type="datetime-local"]::-webkit-datetime-edit-hour-field,
        input[type="datetime-local"]::-webkit-datetime-edit-minute-field {
            background: transparent;
            color: #333;
            font-weight: 500;
            padding: 2px 4px;
            border-radius: 4px;
            transition: background-color 0.2s ease;
        }

        input[type="datetime-local"]::-webkit-datetime-edit-month-field:focus,
        input[type="datetime-local"]::-webkit-datetime-edit-day-field:focus,
        input[type="datetime-local"]::-webkit-datetime-edit-year-field:focus,
        input[type="datetime-local"]::-webkit-datetime-edit-hour-field:focus,
        input[type="datetime-local"]::-webkit-datetime-edit-minute-field:focus {
            background-color: rgba(52, 152, 219, 0.1);
            outline: none;
        }

        /* 时间选择器标签美化 */
        .datetime-group {
            position: relative;
        }

        .datetime-group label {
            display: flex;
            align-items: center;
            margin-bottom: 8px;
            font-weight: 500;
            color: #555;
        }

        .datetime-group label::before {
            content: '🕐';
            margin-right: 8px;
            font-size: 16px;
        }

        /* 添加时间提示 */
        .datetime-hint {
            font-size: 12px;
            color: #666;
            margin-top: 4px;
            opacity: 0.8;
        }

        .btn {
            background: #3498db;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 8px;
            font-size: 16px;
            cursor: pointer;
            transition: background 0.3s;
            margin-right: 10px;
            margin-bottom: 10px;
        }

        .btn:hover {
            background: #2980b9;
        }

        .btn-danger {
            background: #e74c3c;
        }

        .btn-danger:hover {
            background: #c0392b;
        }

        .btn-success {
            background: #27ae60;
        }

        .btn-success:hover {
            background: #229954;
        }

        .result {
            margin-top: 20px;
            padding: 15px;
            border-radius: 8px;
            display: none;
        }

        .result.success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }

        .result.error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }

        .record-info {
            background: #e8f4fd;
            padding: 15px;
            border-radius: 8px;
            margin-top: 15px;
        }

        .record-info img {
            max-width: 200px;
            border-radius: 8px;
            margin-top: 10px;
        }

        /* 动画效果 */
        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(10px); }
            to { opacity: 1; transform: translateY(0); }
        }

        .card {
            animation: fadeIn 0.5s ease-out;
        }

        /* 时间选择器的动画效果 */
        input[type="datetime-local"] {
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        }

        input[type="datetime-local"]:focus {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(52, 152, 219, 0.15);
        }

        /* 提示文字的动画 */
        .datetime-hint {
            transition: all 0.3s ease;
        }

        /* 图片选择器样式 */
        .image-upload-container {
            position: relative;
            border: 2px dashed #e1e8ed;
            border-radius: 8px;
            padding: 20px;
            text-align: center;
            background: linear-gradient(135deg, #fff 0%, #f8f9fa 100%);
            transition: all 0.3s ease;
            cursor: pointer;
        }

        .image-upload-container:hover {
            border-color: #3498db;
            background: linear-gradient(135deg, #fff 0%, #f0f8ff 100%);
        }

        .image-upload-container.dragover {
            border-color: #27ae60;
            background: linear-gradient(135deg, #fff 0%, #f0fff4 100%);
        }

        .image-upload-input {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            opacity: 0;
            cursor: pointer;
        }

        .image-upload-content {
            pointer-events: none;
        }

        .image-upload-icon {
            font-size: 48px;
            color: #3498db;
            margin-bottom: 10px;
        }

        .image-upload-text {
            color: #666;
            font-size: 14px;
            margin-bottom: 5px;
        }

        .image-upload-hint {
            color: #999;
            font-size: 12px;
        }

        /* 图片预览样式 */
        .image-preview {
            margin-top: 15px;
            text-align: center;
        }

        .image-preview img {
            max-width: 200px;
            max-height: 200px;
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }

        .image-info {
            margin-top: 10px;
            font-size: 12px;
            color: #666;
        }

        .image-actions {
            margin-top: 10px;
        }

        .btn-small {
            padding: 6px 12px;
            font-size: 12px;
            margin: 0 5px;
        }

        /* 压缩进度条 */
        .compress-progress {
            margin-top: 10px;
            background: #f0f0f0;
            border-radius: 4px;
            overflow: hidden;
            height: 4px;
            display: none;
        }

        .compress-progress-bar {
            height: 100%;
            background: linear-gradient(90deg, #3498db, #2ecc71);
            width: 0%;
            transition: width 0.3s ease;
        }

        /* 记录列表样式 */
        .records-container {
            max-height: 500px;
            overflow-y: auto;
            border: 1px solid #e1e8ed;
            border-radius: 8px;
            background: #fff;
        }

        .record-item {
            padding: 15px;
            border-bottom: 1px solid #f0f0f0;
            transition: background-color 0.2s ease;
        }

        .record-item:last-child {
            border-bottom: none;
        }

        .record-item:hover {
            background-color: #f8f9fa;
        }

        .record-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 10px;
        }

        .record-phone {
            font-weight: 600;
            color: #2c3e50;
            font-size: 16px;
        }

        .record-date {
            color: #666;
            font-size: 14px;
        }

        .record-date.expired {
            color: #e74c3c;
            font-weight: 500;
        }

        .record-date.active {
            color: #27ae60;
            font-weight: 500;
        }

        .record-image {
            text-align: center;
            margin-top: 10px;
        }

        .record-image img {
            max-width: 150px;
            max-height: 150px;
            border-radius: 6px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            cursor: pointer;
            transition: transform 0.2s ease;
        }

        .record-image img:hover {
            transform: scale(1.05);
        }

        .record-actions {
            margin-top: 10px;
            text-align: right;
        }

        .loading {
            text-align: center;
            padding: 20px;
            color: #666;
        }

        .empty-state {
            text-align: center;
            padding: 40px 20px;
            color: #999;
        }

        .empty-state-icon {
            font-size: 48px;
            margin-bottom: 15px;
        }

        .stats-bar {
            display: flex;
            justify-content: space-around;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 15px;
        }

        .stat-item {
            text-align: center;
        }

        .stat-number {
            font-size: 24px;
            font-weight: bold;
            display: block;
        }

        .stat-label {
            font-size: 12px;
            opacity: 0.9;
        }

        @media (max-width: 768px) {
            .container {
                padding: 15px;
            }

            .header h1 {
                font-size: 1.5rem;
            }

            .card {
                padding: 20px;
            }

            .btn {
                width: 100%;
                margin-right: 0;
            }

            /* 移动端时间选择器优化 */
            input[type="datetime-local"] {
                font-size: 16px; /* 防止iOS缩放 */
                padding: 14px 16px;
            }

            .datetime-group label::before {
                font-size: 14px;
            }

            .datetime-hint {
                font-size: 11px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>抖音微信管理</h1>
        </div>

        <!-- 查询记录 -->
        <div class="card">
            <h3>查询记录</h3>
            <div class="form-group">
                <label for="queryPhone">手机号</label>
                <input type="tel" id="queryPhone" placeholder="请输入手机号">
            </div>
            <button class="btn" onclick="queryRecord()">查询</button>
            <div id="queryResult" class="result"></div>
        </div>

        <!-- 添加/更新记录 -->
        <div class="card">
            <h3>添加/更新记录</h3>
            <div class="form-group">
                <label for="phone">手机号</label>
                <input type="tel" id="phone" placeholder="请输入手机号">
            </div>
            <div class="form-group">
                <label for="img">📷 选择图片</label>
                <div class="image-upload-container" onclick="document.getElementById('imageInput').click()">
                    <input type="file" id="imageInput" class="image-upload-input" accept="image/*">
                    <div class="image-upload-content">
                        <div class="image-upload-icon">📁</div>
                        <div class="image-upload-text">点击选择图片或拖拽图片到此处</div>
                        <div class="image-upload-hint">支持 JPG、PNG、GIF 格式，自动压缩</div>
                    </div>
                </div>
                <div class="compress-progress">
                    <div class="compress-progress-bar"></div>
                </div>
                <div id="imagePreview" class="image-preview" style="display: none;">
                    <img id="previewImg" src="" alt="预览图片">
                    <div class="image-info">
                        <span id="imageSize"></span> | <span id="imageType"></span>
                    </div>
                    <div class="image-actions">
                        <button type="button" class="btn btn-small" onclick="clearImage()">重新选择</button>
                    </div>
                </div>
                <input type="hidden" id="img" value="">
            </div>
            <div class="form-group datetime-group">
                <label for="date">到期时间</label>
                <input type="datetime-local" id="date" placeholder="请选择到期时间">
                <div class="datetime-hint">请选择具体的到期日期和时间</div>
            </div>
            <button class="btn btn-success" onclick="addRecord()">添加记录</button>
            <button class="btn" onclick="updateRecord()">更新记录</button>
            <div id="operationResult" class="result"></div>
        </div>

        <!-- 删除记录 -->
        <div class="card">
            <h3>删除记录</h3>
            <div class="form-group">
                <label for="deletePhone">手机号</label>
                <input type="tel" id="deletePhone" placeholder="请输入要删除的手机号">
            </div>
            <button class="btn btn-danger" onclick="deleteRecord()">删除记录</button>
            <div id="deleteResult" class="result"></div>
        </div>

        <!-- 所有记录 -->
        <div class="card">
            <h3>📋 所有记录</h3>
            <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 15px;">
                <button class="btn" onclick="getAllRecords()">🔄 刷新记录</button>
                <div style="font-size: 14px; color: #666;">
                    <span id="recordCount">点击刷新获取记录</span>
                </div>
            </div>

            <!-- 统计信息 -->
            <div id="statsBar" class="stats-bar" style="display: none;">
                <div class="stat-item">
                    <span class="stat-number" id="totalCount">0</span>
                    <span class="stat-label">总记录</span>
                </div>
                <div class="stat-item">
                    <span class="stat-number" id="activeCount">0</span>
                    <span class="stat-label">有效记录</span>
                </div>
                <div class="stat-item">
                    <span class="stat-number" id="expiredCount">0</span>
                    <span class="stat-label">已过期</span>
                </div>
            </div>

            <div id="allRecordsResult" class="result"></div>

            <!-- 记录列表容器 -->
            <div id="recordsList" class="records-container" style="display: none;">
                <!-- 记录将在这里动态加载 -->
            </div>
        </div>
    </div>

    <script>
        const API_BASE = '/api';

        function showResult(elementId, message, isSuccess) {
            const element = document.getElementById(elementId);
            element.textContent = message;
            element.className = `result ${isSuccess ? 'success' : 'error'}`;
            element.style.display = 'block';
            setTimeout(() => {
                element.style.display = 'none';
            }, 3000);
        }

        // 时间戳转换为本地时间字符串（用于datetime-local输入框）
        function timestampToDatetimeLocal(timestamp) {
            
            
            const date = new Date(parseInt(timestamp));
            const year = date.getFullYear();
            const month = String(date.getMonth() + 1).padStart(2, '0');
            const day = String(date.getDate()).padStart(2, '0');
            const hours = String(date.getHours()).padStart(2, '0');
            const minutes = String(date.getMinutes()).padStart(2, '0');
            return `${year}-${month}-${day}T${hours}:${minutes}`;
        }

        // 本地时间字符串转换为时间戳
        function datetimeLocalToTimestamp(datetimeLocal) {
            return new Date(datetimeLocal).getTime();
        }

        // 时间戳转换为可读的时间格式
        function timestampToReadable(timestamp) {
            
            const date = new Date(parseInt(timestamp));
            return date.toLocaleString('zh-CN', {
                year: 'numeric',
                month: '2-digit',
                day: '2-digit',
                hour: '2-digit',
                minute: '2-digit'
            });
        }

        // 设置默认时间（当前时间+1小时）
        function setDefaultDateTime() {
            const now = new Date();
            now.setHours(now.getHours() + 1); // 默认设置为1小时后
            const defaultDateTime = timestampToDatetimeLocal(now.getTime());
            document.getElementById('date').value = defaultDateTime;
        }

        // 验证时间是否在未来
        function validateDateTime(datetimeLocal) {
            const selectedTime = new Date(datetimeLocal);
            const now = new Date();
            return selectedTime > now;
        }

        // 图片压缩函数
        function compressImage(file, maxWidth = 800, maxHeight = 600, quality = 0.8) {
            return new Promise((resolve) => {
                const canvas = document.createElement('canvas');
                const ctx = canvas.getContext('2d');
                const img = new Image();

                img.onload = function() {
                    // 计算压缩后的尺寸
                    let { width, height } = img;

                    if (width > height) {
                        if (width > maxWidth) {
                            height = (height * maxWidth) / width;
                            width = maxWidth;
                        }
                    } else {
                        if (height > maxHeight) {
                            width = (width * maxHeight) / height;
                            height = maxHeight;
                        }
                    }

                    canvas.width = width;
                    canvas.height = height;

                    // 绘制压缩后的图片
                    ctx.drawImage(img, 0, 0, width, height);

                    // 转换为base64
                    const compressedDataUrl = canvas.toDataURL('image/jpeg', quality);
                    resolve(compressedDataUrl);
                };

                img.src = URL.createObjectURL(file);
            });
        }

        // 格式化文件大小
        function formatFileSize(bytes) {
            if (bytes === 0) return '0 Bytes';
            const k = 1024;
            const sizes = ['Bytes', 'KB', 'MB', 'GB'];
            const i = Math.floor(Math.log(bytes) / Math.log(k));
            return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
        }

        // 处理图片选择
        async function handleImageSelect(file) {
            if (!file || !file.type.startsWith('image/')) {
                showResult('operationResult', '请选择有效的图片文件', false);
                return;
            }

            // 显示压缩进度
            const progressContainer = document.querySelector('.compress-progress');
            const progressBar = document.querySelector('.compress-progress-bar');
            progressContainer.style.display = 'block';
            progressBar.style.width = '0%';

            try {
                // 模拟压缩进度
                progressBar.style.width = '30%';

                // 压缩图片
                const compressedBase64 = await compressImage(file);
                progressBar.style.width = '80%';

                // 显示预览
                const preview = document.getElementById('imagePreview');
                const previewImg = document.getElementById('previewImg');
                const imageSize = document.getElementById('imageSize');
                const imageType = document.getElementById('imageType');
                const hiddenInput = document.getElementById('img');

                previewImg.src = compressedBase64;
                imageSize.textContent = formatFileSize(compressedBase64.length * 0.75); // base64大约比原文件大25%
                imageType.textContent = file.type;
                hiddenInput.value = compressedBase64;

                preview.style.display = 'block';
                progressBar.style.width = '100%';

                // 隐藏进度条
                setTimeout(() => {
                    progressContainer.style.display = 'none';
                    progressBar.style.width = '0%';
                }, 500);

            } catch (error) {
                showResult('operationResult', '图片处理失败', false);
                progressContainer.style.display = 'none';
            }
        }

        // 清除图片
        function clearImage() {
            document.getElementById('imagePreview').style.display = 'none';
            document.getElementById('img').value = '';
            document.getElementById('imageInput').value = '';
        }

        // 页面加载时设置默认时间和图片处理
        document.addEventListener('DOMContentLoaded', function() {
            setDefaultDateTime();

            // 自动获取所有记录
            getAllRecords();

            // 为时间输入框添加实时验证
            const dateInput = document.getElementById('date');
            dateInput.addEventListener('change', function() {
                const hint = this.parentNode.querySelector('.datetime-hint');
                if (this.value && !validateDateTime(this.value)) {
                    hint.textContent = '⚠️ 请选择未来的时间';
                    hint.style.color = '#e74c3c';
                } else if (this.value) {
                    const selectedDate = new Date(this.value);
                    hint.textContent = `✅ 已选择: ${selectedDate.toLocaleString('zh-CN')}`;
                    hint.style.color = '#27ae60';
                } else {
                    hint.textContent = '请选择具体的到期日期和时间';
                    hint.style.color = '#666';
                }
            });

            // 图片选择处理
            const imageInput = document.getElementById('imageInput');
            const uploadContainer = document.querySelector('.image-upload-container');

            imageInput.addEventListener('change', function(e) {
                const file = e.target.files[0];
                if (file) {
                    handleImageSelect(file);
                }
            });

            // 拖拽上传
            uploadContainer.addEventListener('dragover', function(e) {
                e.preventDefault();
                this.classList.add('dragover');
            });

            uploadContainer.addEventListener('dragleave', function(e) {
                e.preventDefault();
                this.classList.remove('dragover');
            });

            uploadContainer.addEventListener('drop', function(e) {
                e.preventDefault();
                this.classList.remove('dragover');
                const file = e.dataTransfer.files[0];
                if (file) {
                    handleImageSelect(file);
                }
            });
        });

        async function queryRecord() {
            const phone = document.getElementById('queryPhone').value;
            if (!phone) {
                showResult('queryResult', '请输入手机号', false);
                return;
            }

            try {
                const response = await fetch(`${API_BASE}/auth/getDyWx?phone=${phone}`);
                const data = await response.json();
                
                if (data.code === 200 && data.data.length > 0) {
                    const record = data.data[0];
                    document.getElementById('queryResult').innerHTML = `
                        <div class="record-info">
                            <p><strong>手机号:</strong> ${record.phone}</p>
                            <p><strong>到期时间:</strong> ${timestampToReadable(record.date)}</p>
                            <p><strong>图片:</strong></p>
                            <img src="${record.img}" alt="记录图片" onerror="this.style.display='none'">
                        </div>
                    `;
                    document.getElementById('queryResult').className = 'result success';
                    document.getElementById('queryResult').style.display = 'block';
                } else {
                    showResult('queryResult', '未找到记录', false);
                }
            } catch (error) {
                showResult('queryResult', '查询失败', false);
            }
        }

        async function addRecord() {
            const phone = document.getElementById('phone').value;
            const img = document.getElementById('img').value;
            const dateInput = document.getElementById('date').value;

            if (!phone || !img || !dateInput) {
                showResult('operationResult', '请填写所有字段（包括图片）', false);
                return;
            }

            // 验证时间是否在未来
            if (!validateDateTime(dateInput)) {
                showResult('operationResult', '到期时间必须是未来时间', false);
                return;
            }

            const timestamp = datetimeLocalToTimestamp(dateInput);

            try {
                const response = await fetch(`${API_BASE}/auth/addDyWx`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({ phone, img, date: timestamp.toString() })
                });
                const data = await response.json();
                showResult('operationResult', data.msg, data.code === 200);
                
                if (data.code === 200) {
                    document.getElementById('phone').value = '';
                    clearImage();
                    setDefaultDateTime();
                    // 添加成功后刷新记录列表
                    getAllRecords();
                }
            } catch (error) {
                showResult('operationResult', '添加失败', false);
            }
        }

        async function updateRecord() {
            const phone = document.getElementById('phone').value;
            const img = document.getElementById('img').value;
            const dateInput = document.getElementById('date').value;

            if (!phone) {
                showResult('operationResult', '请输入手机号', false);
                return;
            }

            let timestamp = null;
            if (dateInput) {
                // 验证时间是否在未来
                if (!validateDateTime(dateInput)) {
                    showResult('operationResult', '到期时间必须是未来时间', false);
                    return;
                }
                timestamp = datetimeLocalToTimestamp(dateInput);
            }

            try {
                const response = await fetch(`${API_BASE}/auth/updateDyWx`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({ 
                        phone, 
                        img, 
                        date: timestamp ? timestamp.toString() : null 
                    })
                });
                const data = await response.json();
                showResult('operationResult', data.msg, data.code === 200);

                if (data.code === 200) {
                    // 更新成功后刷新记录列表
                    getAllRecords();
                }
            } catch (error) {
                showResult('operationResult', '更新失败', false);
            }
        }

        async function deleteRecord() {
            const phone = document.getElementById('deletePhone').value;
            if (!phone) {
                showResult('deleteResult', '请输入手机号', false);
                return;
            }

            if (!confirm('确定要删除这条记录吗？')) {
                return;
            }

            try {
                const response = await fetch(`${API_BASE}/auth/deleteDyWx?phone=${phone}`, {
                    method: 'DELETE'
                });
                const data = await response.json();
                showResult('deleteResult', data.msg, data.code === 200);

                if (data.code === 200) {
                    document.getElementById('deletePhone').value = '';
                    // 删除成功后刷新记录列表
                    getAllRecords();
                }
            } catch (error) {
                showResult('deleteResult', '删除失败', false);
            }
        }

        // 获取所有记录
        async function getAllRecords() {
            const recordsList = document.getElementById('recordsList');
            const statsBar = document.getElementById('statsBar');
            const recordCount = document.getElementById('recordCount');

            // 显示加载状态
            recordsList.innerHTML = '<div class="loading">🔄 正在加载记录...</div>';
            recordsList.style.display = 'block';
            recordCount.textContent = '正在加载...';

            try {
                const response = await fetch(`${API_BASE}/auth/getAll`);
                const data = await response.json();

                if (data.code === 200) {
                    const records = data.data || [];
                    displayAllRecords(records);
                    updateStats(records);
                    showResult('allRecordsResult', `成功获取 ${records.length} 条记录`, true);
                } else {
                    recordsList.innerHTML = '<div class="empty-state"><div class="empty-state-icon">📭</div><div>获取记录失败</div></div>';
                    showResult('allRecordsResult', data.msg || '获取记录失败', false);
                }
            } catch (error) {
                recordsList.innerHTML = '<div class="empty-state"><div class="empty-state-icon">❌</div><div>网络错误，请稍后重试</div></div>';
                showResult('allRecordsResult', '获取记录失败', false);
            }
        }

        // 显示所有记录
        function displayAllRecords(records) {
            const recordsList = document.getElementById('recordsList');

            if (records.length === 0) {
                recordsList.innerHTML = `
                    <div class="empty-state">
                        <div class="empty-state-icon">📭</div>
                        <div>暂无记录</div>
                        <div style="font-size: 12px; margin-top: 5px; color: #999;">
                            点击上方"添加记录"来创建第一条记录
                        </div>
                    </div>
                `;
                return;
            }

            const currentTime = new Date().getTime();
            let html = '';

            records.forEach(record => {
                const expireTime = parseInt(record.date);
                const isExpired = expireTime < currentTime;
                const dateClass = isExpired ? 'expired' : 'active';
                const statusText = isExpired ? '已过期' : '有效';
                const statusIcon = isExpired ? '⏰' : '✅';

                html += `
                    <div class="record-item">
                        <div class="record-header">
                            <div class="record-phone">📱 ${record.phone}</div>
                            <div class="record-date ${dateClass}">
                                ${statusIcon} ${statusText} - ${timestampToReadable(record.date)}
                            </div>
                        </div>
                        <div class="record-image">
                            <img src="${record.img}" alt="记录图片"
                                 onclick="openImageModal('${record.img}')"
                                 onerror="this.style.display='none'; this.parentNode.innerHTML='<div style=\\'color: #999; padding: 20px;\\'>图片加载失败</div>'">
                        </div>
                        <div class="record-actions">
                            <button class="btn btn-small" onclick="editRecord('${record.phone}', '${record.img}', '${record.date}')">✏️ 编辑</button>
                            <button class="btn btn-small btn-danger" onclick="quickDeleteRecord('${record.phone}')">🗑️ 删除</button>
                        </div>
                    </div>
                `;
            });

            recordsList.innerHTML = html;
        }

        // 更新统计信息
        function updateStats(records) {
            const statsBar = document.getElementById('statsBar');
            const totalCount = document.getElementById('totalCount');
            const activeCount = document.getElementById('activeCount');
            const expiredCount = document.getElementById('expiredCount');
            const recordCount = document.getElementById('recordCount');

            const currentTime = new Date().getTime();
            const active = records.filter(record => parseInt(record.date) >= currentTime).length;
            const expired = records.length - active;

            totalCount.textContent = records.length;
            activeCount.textContent = active;
            expiredCount.textContent = expired;
            recordCount.textContent = `共 ${records.length} 条记录`;

            statsBar.style.display = 'flex';
        }

        // 快速删除记录
        async function quickDeleteRecord(phone) {
            if (!confirm(`确定要删除手机号 ${phone} 的记录吗？`)) {
                return;
            }

            try {
                const response = await fetch(`${API_BASE}/auth/deleteDyWx?phone=${phone}`, {
                    method: 'DELETE'
                });
                const data = await response.json();

                if (data.code === 200) {
                    showResult('allRecordsResult', '删除成功', true);
                    getAllRecords(); // 刷新列表
                } else {
                    showResult('allRecordsResult', data.msg || '删除失败', false);
                }
            } catch (error) {
                showResult('allRecordsResult', '删除失败', false);
            }
        }

        // 编辑记录（填充到表单）
        function editRecord(phone, img, date) {
            document.getElementById('phone').value = phone;
            document.getElementById('img').value = img;

            // 显示图片预览
            if (img) {
                const preview = document.getElementById('imagePreview');
                const previewImg = document.getElementById('previewImg');
                const imageSize = document.getElementById('imageSize');
                const imageType = document.getElementById('imageType');

                previewImg.src = img;
                imageSize.textContent = formatFileSize(img.length * 0.75);
                imageType.textContent = 'image/jpeg';
                preview.style.display = 'block';
            }

            // 设置时间
            const dateTimeLocal = timestampToDatetimeLocal(date);
            document.getElementById('date').value = dateTimeLocal;

            // 滚动到表单区域
            document.querySelector('.card:nth-child(3)').scrollIntoView({ behavior: 'smooth' });

            showResult('allRecordsResult', `已加载 ${phone} 的记录到编辑表单`, true);
        }

        // 打开图片模态框
        function openImageModal(imgSrc) {
            // 创建模态框
            const modal = document.createElement('div');
            modal.style.cssText = `
                position: fixed;
                top: 0;
                left: 0;
                width: 100%;
                height: 100%;
                background: rgba(0,0,0,0.8);
                display: flex;
                justify-content: center;
                align-items: center;
                z-index: 1000;
                cursor: pointer;
            `;

            const img = document.createElement('img');
            img.src = imgSrc;
            img.style.cssText = `
                max-width: 90%;
                max-height: 90%;
                border-radius: 8px;
                box-shadow: 0 4px 20px rgba(0,0,0,0.3);
            `;

            modal.appendChild(img);
            document.body.appendChild(modal);

            // 点击关闭
            modal.addEventListener('click', () => {
                document.body.removeChild(modal);
            });
        }
    </script>
</body>
</html>


