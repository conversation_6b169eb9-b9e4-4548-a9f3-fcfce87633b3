# 腾讯云IM集成说明

## 概述

本项目已集成腾讯云IM功能，实现了创建活动时自动创建群聊，用户加入活动时自动加入对应群聊的功能。

## 配置步骤

### 1. 腾讯云IM应用配置

1. 登录腾讯云控制台
2. 进入即时通信IM产品页面
3. 创建新的IM应用或使用现有应用
4. 获取SDKAppID和密钥
5. **重要**：在IM控制台的"开发配置"中设置管理员账号
   - 进入应用详情页面
   - 点击"开发配置"
   - 在"账号管理员"部分添加管理员账号（建议使用系统管理员ID，如"1"）

### 2. 环境变量配置

复制`.env.example`文件为`.env`，并填入正确的配置信息：

```bash
cp .env.example .env
```

修改`.env`文件中的IM配置：

```env
IM_SDK_APP_ID=你的SDKAppID
IM_SECRET_KEY=你的密钥
```

### 3. 数据库更新

执行数据库更新脚本，为sports_events表添加group_id字段：

```sql
source database/add_group_id.sql
```

### 4. 安装依赖

项目已自动安装了必要的依赖包：
- tencentcloud-sdk-nodejs
- tls-sig-api-v2

### 5. 测试IM配置

运行测试脚本验证IM配置是否正确：

```bash
node test/im-test.js
```

如果配置正确，应该看到类似输出：
```
开始测试IM服务...

1. 测试UserSig生成...
UserSig生成成功: ✓

2. 测试管理员UserSig生成...
管理员UserSig生成成功: ✓

3. 测试管理员账号导入...
管理员账号导入成功 ✓

4. 测试创建群组...
群组创建成功 ✓

5. 测试解散群组...
群组解散成功 ✓

✅ IM服务测试完成
```

## 功能说明

### 后端功能

#### 1. 用户认证接口修改

- `/api/auth/verify` - 验证接口现在返回IM配置
- `/api/auth/wxLogin` - 微信登录接口现在返回IM配置
- `/api/auth/imConfig` - 新增获取IM配置的独立接口

返回格式：
```json
{
  "code": 200,
  "msg": "登录成功",
  "data": {
    "token": "Bearer xxx",
    "user": {...},
    "imConfig": {
      "SDKAppID": 123456789,
      "userID": "user_id",
      "userSig": "generated_user_sig"
    }
  }
}
```

#### 2. 活动管理接口修改

- `/api/sports/create` - 创建活动时自动创建IM群组
- `/api/sports/join` - 加入活动时自动加入群组
- `/api/sports/quit` - 退出活动时自动退出群组
- `/api/sports/cancel` - 取消活动时自动解散群组
- `/api/sports/getEventGroup` - 获取活动群组信息

#### 3. IM管理接口

- `/api/im/createGroup` - 创建群组
- `/api/im/addGroupMembers` - 添加群成员
- `/api/im/deleteGroupMembers` - 删除群成员
- `/api/im/destroyGroup` - 解散群组
- `/api/im/getGroupInfo` - 获取群组信息
- `/api/im/getGroupMemberInfo` - 获取群成员信息

### 数据流程

#### 创建活动流程
1. 用户创建活动
2. 后端创建活动记录
3. 自动创建IM群组（群组ID格式：`event_${活动ID}`）
4. 将群组ID保存到活动记录中
5. 活动创建者自动成为群主

#### 加入活动流程
1. 用户加入活动
2. 后端添加用户到参与者表
3. 自动将用户添加到IM群组
4. 用户可以进入群聊

#### 退出活动流程
1. 用户退出活动
2. 后端从参与者表删除用户
3. 自动将用户从IM群组移除

#### 取消活动流程
1. 活动创建者取消活动
2. 后端更新活动状态
3. 自动解散IM群组

## 错误处理

### 群组操作失败处理
- 群组创建失败不影响活动创建
- 群组成员操作失败不影响活动参与
- 群组解散失败不影响活动取消
- 所有IM操作异常都会记录日志但不中断主流程

### 常见错误码
- `ErrorCode: 0` - 操作成功
- `ErrorCode: 10002` - 服务器内部错误
- `ErrorCode: 10003` - 请求命令字非法
- `ErrorCode: 10004` - 参数非法
- `ErrorCode: 10007` - 操作权限不足

## 注意事项

1. **安全性**：UserSig必须在服务端生成，不能在客户端生成
2. **性能**：群组成员数量建议控制在合理范围内
3. **费用**：注意腾讯云IM的免费额度限制（100 DAU）
4. **兼容性**：确保在各个平台上正常运行
5. **数据同步**：确保活动状态与群组状态保持一致

## 测试

### 功能测试清单
- [ ] 创建活动时群组自动创建
- [ ] 加入活动时自动加入群组
- [ ] 退出活动时自动退出群组
- [ ] 取消活动时群组自动解散
- [ ] 获取活动群组信息
- [ ] IM配置正确返回

### 测试用例
1. 创建活动并验证群组创建
2. 多用户加入活动并验证群组成员
3. 用户退出活动并验证群组成员变化
4. 取消活动并验证群组解散

## 故障排除

### 常见问题

#### 1. "set the identifier field of the RESTful API request to the admin account of the app" 错误

**原因**：管理员账号未在腾讯云IM控制台中设置

**解决方案**：
1. 登录腾讯云IM控制台
2. 进入应用详情页面
3. 点击"开发配置"
4. 在"账号管理员"部分添加管理员账号（与config/im.js中的adminUserId一致）

#### 2. UserSig生成失败
**检查项**：
- SDKAppID和密钥配置是否正确
- 密钥是否与应用匹配

#### 3. 群组创建失败
**检查项**：
- 网络连接是否正常
- 腾讯云IM服务状态
- 管理员账号是否已导入

#### 4. 成员操作失败
**检查项**：
- 用户ID格式是否正确
- 用户账号是否已导入到IM系统
- 操作权限是否足够

### 日志查看
所有IM操作都会记录详细日志，可以通过控制台查看：
```bash
# 查看应用日志
npm run dev
```

## 扩展功能

### 可扩展的功能
1. 群聊消息推送
2. 群组公告管理
3. 群组权限控制
4. 消息历史记录
5. 文件共享功能
